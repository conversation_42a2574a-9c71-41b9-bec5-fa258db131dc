{"FILE_MD5": "000024", "PARSED_CONTENT": "# Security-related Commits in Open Source Web Browser Projects\n\nA´ kos Kiss   \nDepartment of Software Engineering   \nUniversity of Szeged   \nSzeged, Hungary   \n<EMAIL>   \nRena´ta Hodova´n   \nDepartment of Software Engineering   \nUniversity of Szeged   \nSzeged, Hungary   \n<EMAIL>\n\nAbstract—The security of web browsers is of paramount importance, these days perhaps more than ever. Unfortunately, acquiring real data for security-related research is not an easy task, as access to sensitive information is rarely granted to researchers who are not members of a trusted security team. In this paper, we describe a method to mine security-related commits from open source software repositories, even if the reports of already fixed security issues have access restrictions, and we show the applicability of the method on two popular web browser projects. We also made the mined dataset available, listing more than 13,000 security-related commits, with which we hope to facilitate research on security-targeted bug prediction.\n\nIndex Terms—security, commits, web, browser “Those who cannot remember the past are condemned to repeat it.”\n\n—<PERSON>\n\ndiscusses the application of the methodology to real projects as well as the extracted data. Finally, Section IV concludes the paper with potential use-cases of the presented dataset, thus giving directions for future research.\n\n# II. MINING OPEN SOURCE PROJECTS FOR SECURITY-RELATED INFORMATION\n\nIdentifying historical security-related issues in software projects (and accessing the security reports in issue trackers) is not an easy task for researchers, not even in open source projects, as access to sensitive information is rarely granted to “outsiders”, i.e., to those who are not members of a trusted and privileged security team. Although access restrictions to some issues may be lifted after some time, especially for issues already fixed, this practice is neither general to all projects nor is it dependable (as the time after which an issue is opened up to the public, if ever, can vary heavily).\n\n# I. INTRODUCTION\n\nThe web is becoming the most prevalent platform these days and web browsers are the “operating systems” of this platform [1], [2]. Thus, their security is of paramount importance – naturally for web browser developers, but for security researchers as well [3]. Unfortunately, acquiring real data for security-related research is not an easy task because of the sensitive nature of the topic. Quite often, security reports remain closed from the public even after a fix has been applied, hindering even the identification of the fixes.\n\nIn this paper, we present a way to extract past securityrelated commits from open source software repositories. We apply our technique to the repositories of the WebKit [4] and Mozilla [5] projects, those behind the Safari and Firefox web browsers (which are currently the $2 ^ { \\mathrm { n d } }$ and $3 ^ { \\mathrm { r d } } { \\ - } \\mathrm { - } 4 ^ { \\mathrm { t h } }$ most popular browsers, respectively, according to the June 2019 market share reports [6]–[8]), and publish the extracted dataset for reuse by the security researcher community. It must be noted that the presented approach does not go against the norms of security research, i.e., it works with data about already fixed issues that are publicly available, which are not that easy to gather, though.\n\nThe rest of the paper is organized as follows: Section II describes the methodology of extracting security-related commits from open source software repositories, while Section III\n\nHowever, data stored in issue trackers is not the only source of security-related information about a project. If a project has strict enough guidelines for development, then each step (i.e., commit in the version control system of the project) may be linked to an issue in the tracker by mentioning its identifier in the commit message (either in plain text or in a URL form, which can vary across projects), independently from the visibility of the issue. Thus, if at least the commit history of a project is available, which is common for open source development, then one can identify at least the identifiers of the security-related issues of a project by collecting all referenced issue identifiers from the commit messages, and by querying the issue tracker for the collected identifiers. If the tracker grants access to the details of an issue, then its security status can be trivially determined. However, if the tracker denies access to an issue, then its restricted visibility contains information on its own and signals that it may be a valid assumption to flag that issue (identifier) as securityrelated. As a final step, the so-identified security-related issue identifiers can be matched to the commits that reference them.\n\nThe above described idea is formally defined in the algorithm in Figure 1. The algorithm is deliberately high-level with some helper functions left unspecified to enable the tailoring of the algorithm to the specifics of various projects. (How commits can be enumerated can vary from one version\n\nprocedure MINESECURITYCOMMITS(commit history issue tracker) (\\* Collect issue IDs \\*) issue ids $= \\emptyset$ forall commit in commit history do issue ids $\\cup =$ ISSUEIDSINCOMMIT(commit) end forall (\\* Identify security related issue IDs \\*) security issue ${ i d s = \\emptyset }$ forall issue id in issue ids do response $\\mathbf { \\tau } = \\mathbf { \\tau }$ QUERY(issue tracker, issue id) if ACCESSGRANTED(response) then if SECURITYRELATED(response) then security issue ids $\\cup = \\{ i s s u e \\_ i d \\}$ end if else if ACCESSDENIED(response) then security issue ids $\\cup = \\{ i s s u e \\_ i d \\}$ end if end forall (\\* Match security related issue IDs to commits \\*) security commits ${ \\it \\Delta \\phi } = \\emptyset { \\it \\Delta \\Psi }$ forall commit in commit history do if ISSUEIDSINCOMMIT(commit) ∩ security issue ids $\\neq \\emptyset$ then security commits $\\cup =$ commit end if end forall return security commits   \nend procedure\n\ncontrol system – e.g., Git, Subversion, or Mercurial – to another. The format of how issues are referenced in commit messages are project-specific. The mechanisms and APIs for querying issues are different in each tracker – e.g., in Bugzilla, Monorail, Github, GitLab, or Bitbucket. And finally, should an issue query succeed, the identification – or labelling, or tagging – of security-related issues is, again, project-specific. These concepts are captured by the ISSUEIDSINCOMMIT, QUERY, ACCESSGRANTED, ACCESSDENIED, and SECURITYRELATED helper functions.)\n\n# III. SECURITY-RELATED COMMITS OF WEB BROWSER PROJECTS\n\nBecause of the importance of web browsers in today’s software ecosystems, we have decided to apply the idea and the algorithm presented in the previous section to open source web browser projects. As subjects of our mining work, we have selected the WebKit browser engine project [4] that is driving the Safari web browser, and the Mozilla software project [5] that incorporates the components of both the Gecko browser engine and the Firefox web browser. The developers of both projects follow well-defined guidelines and link every commit that is landed in the version control repository to an issue in the tracker [9], [10].\n\nTABLE I SECURITY-RELATED COMMITS AND ISSUE IDS OF THE WEBKIT PROJECT (AUG 24, 2001 – DEC 31, 2018)   \n\n<html><body><table><tr><td></td><td>All</td><td colspan=\"4\">Security-related</td></tr><tr><td></td><td></td><td></td><td></td><td>public</td><td>private</td></tr><tr><td>Commits</td><td>207,598</td><td>2,459</td><td>(1.18%)</td><td></td><td></td></tr><tr><td>Issue IDs</td><td>120,923</td><td>2.074</td><td>(1.72%)</td><td>9</td><td>2.065</td></tr></table></body></html>\n\nFig. 1. Algorithm to mine security-related commits from open source project repositories with restricted visibility security issue reports.   \nTABLE II SECURITY-RELATED COMMITS AND ISSUE IDS OF THE MOZILLA PROJECT (MAR 28, 1998 – DEC 31, 2018)   \n\n<html><body><table><tr><td></td><td>All</td><td colspan=\"4\">Security-related</td></tr><tr><td></td><td></td><td></td><td></td><td>public</td><td>private</td></tr><tr><td>Commits</td><td>630.013</td><td>10,906</td><td>(1.73%)</td><td></td><td></td></tr><tr><td>Issue IDs</td><td>271,447</td><td>6,255</td><td>(2.30%)</td><td>5,518</td><td>737</td></tr></table></body></html>\n\nWe have created an implementation of the algorithm of Figure 1 for these two projects, i.e., we have tailored it to be able to work with the Git repositories, the Bugzilla issue trackers, and the project-specific security classifications of these projects. (Note that although the primary version control systems of WebKit and Mozilla are Subversion and Mercurial, respectively, both have official Git mirrors. Thus, we have chosen to access the commit information over a uniform Git interface in the implementation.) The implementation is a Python 3-based command line tool and outputs its results (i.e., the dataset of security-related commits of the web browser projects) in JSON format backed and documented by a JSON Schema [11]. For the sake of reproducibility, the tool is released to the public as the open source BroSCH project1. Moreover, we have also released the result of the BroSCH tool as the BroSCH Dataset $( r 2 0 I \\delta ) ^ { 2 }$ that lists all securityrelated commits of the two investigated browser projects up until the end of year 2018 (i.e., to be precise, all those that got committed before January 1, 2019, 00:00:00 UTC).\n\nWe have to note that although we are aware of the importance of the Chromium project [12] in the field of web browsers, we did not apply our methodology to it. This is not because of an undisclosed theoretical shortcoming of the technique, but more of a legal restriction of the APIs surrounding the Chromium project. At the time of writing this paper, the v1 API of the Monorail issue tracker is both closed from public and its terms of use prohibit to scrape and build databases with content returned from that API [13, Section 5.e]. Should the legal restrictions be lifted in the future, we will extend the tool with support for Chromium as well as the dataset with the corresponding results.\n\n![](images/67a208f81280afea6106a86aa626768a6fe21a911822c66032d17c81c6ea43d7.jpg)  \nFig. 2. Monthly security-related commits in the WebKit project.\n\n![](images/800fa5dea46ff57c38bf6ef0b088c63f1bce4becdf5f0e480998887e353216c9.jpg)  \nFig. 3. Monthly security-related commits in the Mozilla project.\n\nTables I and II give a summary of the two projects and of the extracted dataset. For the WebKit project, we have covered a bit more than 17 years, while for the Mozilla project the data is from more than two decades. The “all” columns show the commits in the repositories from these periods and the unique issue IDs referenced in these commits. The “security-related” column groups show how many of these issues are flagged as security-related and how many commits reference them, both in absolute and relative terms. For both projects, we also give a classification of the flagged issues: “public” stands for those issues that are publicly available in the trackers and are explicitly marked as security issues, while “private” stands for those with restricted access.\n\nAlthough the tables show that the two projects deal with security issues quite differently – namely that the Mozilla project opens up most of the security issues after some time, while the WebKit project hardly opens up any of them –, the ratios of the security-related commits and issues versus all commits and issues are highly similar in both projects. Thus, even though we could not manually validate all the thousands of commits, this similarity gives us some confidence that the assumption we had (i.e., that the issue IDs with restricted visibility hide security issue reports) was valid.\n\nIn Figures 2 and 3 we also plot the number of securityrelated commits landed monthly in each project. (Please, note that although the first commit in the repository of the WebKit project is from 2001, we could not identify any security-related issue references before 2008, neither “public” nor “private”, so the timeline starts with 2008.) The bottom gray parts of the bars on the charts (if any) denote the commits referencing public security issue reports, while the top white parts of the bars (if any) stand for the commits that reference the restricted issue IDs. In the light of Tables I and II, it comes as no surprise that the chart of the WebKit project is mostly white, while the chart of the Mozilla project is predominantly composed of gray bars. Another interesting difference is that although the data from the Mozilla project show a significant increase in the security-related commits starting from 2012, the chart of the WebKit project shows a remarkable drop after the first quarter of 2013. We strongly believe that this is not the sign of a downward trend in the importance of web browser security, but we presume that this drop is the result of the Chromium project forking the Blink engine from WebKit. If we analyze the trends of the number of security-related commits after 1Q2013, we can observe a rising trend in both projects, which confirms the importance of research on this field.\n\n# IV. SUMMARY AND FUTURE RESEARCH\n\nIn this paper, we have described a method to mine securityrelated commits from open source software repositories, even if the security issue reports have access restrictions. We have also shown the applicability of the method on two popular web browser projects, and made both the implementation of the method and the mined dataset (listing more than 13,000 security-related commits) publicly available.\n\nThe first analysis of the dataset on the trends of securityrelated commits underpins the common knowledge that security of web browsers is an increasingly important topic.\n\nHowever, we think that the dataset will not only be good for retrospective analysis. Previous works have already investigated bug prediction approaches based on features or metrics of source code elements or parts of the sources (i.e., commits) [14]–[16], but without classifying the faults. With this dataset available, which allows researchers to tell security-related and security-unrelated commits apart, we hope to pave the way for security-targeted bug forecasting models. Moreover, we think that not only may the source code components of the commits be valuable, but that the securityrelated commit messages may also be a good corpus for natural language processing research and yield interesting results.\n\nFinally, we plan to apply the here-presented mining method in further projects – not necessarily related to the web domain or open source – to assess its applicability and limitations in a more general context.\n\n# ACKNOWLEDGMENT\n\nThis research was supported by grant TUDFO/47138- 1/2019-ITM of the Ministry for Innovation and Technology, Hungary.\n\n# REFERENCES\n\n[1] S. Shah, “Teflon: Anti-stick for the browser’s attack surface,” Oct. 2008, Hack.LU 2008.   \n[2] A. Taivalsaari and T. Mikkonen, “The web as a software platform: Ten years later,” in Proceedings of the 13th International Conference on Web Information Systems and Technologies - Volume 1: WEBIST. SciTePress, Apr. 2017, pp. 41–50.   \n[3] R. Hodova´n and A´ . Kiss, “Security evolution of the Webkit browser engine,” in Proceedings of the 14th IEEE International Symposium on Web Systems Evolution (WSE 2012). IEEE Computer Society, Sep. 2012, pp. 17–19.   \n[4] Apple Inc., “The WebKit open source project,” https://webkit.org (Accessed July 31, 2019).   \n[5] Mozilla, https://www.mozilla.org (Accessed July 31, 2019).   \n[6] StatCounter, “Global Stats,” http://gs.statcounter.com (Accessed July 31, 2019).   \n[7] Net Applications, “Browser market share,” https://netmarketshare.com (Accessed July 31, 2019).   \n[8] Awio Web Services LLC, “W3Counter: Global web stats,” https://www.w3counter.com/globalstats.php (Accessed July 31, 2019).   \n[9] Apple Inc., “WebKit: Contributing code,” https://webkit.org/ contributing-code/ (Accessed September 16, 2019).   \n[10] Mozilla, “MDN web docs: Committing rules and responsibilities,” https://developer.mozilla.org/en-US/docs/Mozilla/Developer guide/ Committing Rules and Responsibilities (Accessed September 16, 2019).   \n[11] A. Wright and H. Andrews, JSON Schema: A Media Type for Describing JSON Documents, Internet-Draft draft-handrews-json-schema-01 (draft07) ed., Mar. 2018.   \n[12] Google LLC and The Chromium Authors, “The Chromium projects,” https://www.chromium.org (Accessed July 31, 2019).   \n[13] Google LLC, Google APIs Terms of Service (Version: January 16, 2019), https://developers.google.com/terms/ (Accessed July 31, 2019).   \n[14] T. Gyimo´thy, R. Ferenc, and I. Siket, “Empirical validation of objectoriented metrics on open source software for fault prediction,” IEEE Transactions on Software Engineering, vol. 31, no. 10, pp. 897–910, Nov. 2005.   \n[15] A. Marcus, D. Poshyvanyk, and R. Ferenc, “Using the conceptual cohesion of classes for fault prediction in object oriented systems,” IEEE Transactions on Software Engineering, vol. 34, no. 2, pp. 287–300, Mar. 2008.   \n[16] R. Ferenc, “Bug Forecast: A method for automatic bug prediction,” in Proceedings of the 2010 International Conference on Advanced Software Engineering & Its Applications (ASEA 2010), ser. Communications in Computer and Information Science (CCIS), vol. 117. Springer, Dec.   \n2010, pp. 283–295."}