{"FILE_MD5": "000036ba1ba6e85b066455b28a5d32f8", "PARSED_CONTENT": "# $\\mathcal { R } ^ { 4 } \\mathcal { C }$ : A Benchmark for Evaluating RC Systems to Get the Right Answer for the Right Reason\n\nNaoya Inoue1,2 Pontus Stenetorp2,3 Kentaro Inui1,2 1Tohoku University 2RIKEN 3University College London {naoya-i, inui}@ecei.tohoku.ac.jp <EMAIL>\n\n# Abstract\n\nRecent studies have revealed that reading comprehension (RC) systems learn to exploit annotation artifacts and other biases in current datasets. This prevents the community from reliably measuring the progress of RC systems. To address this issue, we introduce $\\mathcal { R } ^ { 4 } \\mathcal { C }$ , a new task for evaluating RC systems’ internal reasoning. $\\mathcal { R } ^ { 4 } \\mathcal { C }$ requires giving not only answers but also derivations: explanations that justify predicted answers. We present a reliable, crowdsourced framework for scalably annotating RC datasets with derivations. We create and publicly release the $\\scriptstyle \\mathcal { R } ^ { 4 } \\mathcal { C }$ dataset, the first, quality-assured dataset consisting of $4 . 6 \\mathrm { k }$ questions, each of which is annotated with 3 reference derivations (i.e. $1 3 . 8 \\mathrm { k }$ derivations). Experiments show that our automatic evaluation metrics using multiple reference derivations are reliable, and that $\\scriptstyle \\mathcal { R } ^ { 4 } \\mathcal { C }$ assesses different skills from an existing benchmark.\n\n# 1 Introduction\n\nWhat was the former band of the member of Mother Love Bone who died just before the release of “Apple”?\n\n# Articles\n\n# Question\n\nTitle: Return to Olympus [1] Return to Olympus is the   \n[o2n]l Iyt awlbausmrebleyathsedalatfetrenr tahtieveb raoncdk hbadnbdroMkaelfnu unkpsahnudn . after lead singer Andrew Wood (later of Mother Love Bone) had died... [3] Stone Gossard had compiled… Title: Mother Love Bone [4] Mother Love Bone was an American rock band that… [5] The band was active from… [6] Frontman Andrew Wood’s personality and compositions helped to catapult the group to... [7] Wood died only days before the scheduled release of the band’s debut album, “Apple”, thus ending the… Explanation Answer Supporting facts (SFs): Malfunkshun [1], [2], [4], [6], [7]   \nOutput R4C: Derivation [Malfunkshun] [Andrew Wood] [Malfunkshun] is is lead singer of is former of [a rock band] [Malfunkshun] [Mother Love Bone] [Andrew Wood] [Andrew Wood] is a member of died just before the [Mother Love Bone] release of [Apple]\n\nReading comprehension (RC) has become a key benchmark for natural language understanding (NLU) systems, and a large number of datasets are now available (Welbl et al., 2018; Koˇcisk\\`y et al., 2018; Yang et al., 2018, i.a.). However, it has been established that these datasets suffer from annotation artifacts and other biases, which may allow systems to “cheat”: Instead of learning to read and comprehend texts in their entirety, systems learn to exploit these biases and find answers via simple heuristics, such as looking for an entity with a particular semantic type (Sugawara et al., 2018; Mudrakarta et al., 2018) (e.g. given a question starting with Who, a system finds a person entity found in a document).\n\nTo address this issue, the community has introduced increasingly more difficult Question Answering (QA) problems, for example, so that answerrelated information is scattered across several articles (Welbl et al., 2018; Yang et al., 2018) (i.e. multi-hop QA). However, recent studies show that such multi-hop QA also has weaknesses (Chen and Durrett, 2019; Min et al., 2019; Jiang et al., 2019), e.g. combining multiple sources of information is not always necessary to find answers. Another direction, which we follow, includes evaluating a systems’ reasoning (Jansen, 2018; Yang et al., 2018; Thorne and Vlachos, 2018; Camburu et al., 2018; Fan et al., 2019; Rajani et al., 2019). In the context of RC, Yang et al. (2018) propose HotpotQA, which requires systems not only to give an answer but also to identify supporting facts (SFs), sentences containing information that supports the answer. SFs are defined as sentences containing information that supports the answer (see “Supporting facts” in Fig. 1 for an example).\n\nAs shown in SFs [1] , [2] , and [7] , however, only a subset of SFs may contribute to the necessary reasoning. For example, [1] states two facts: (a) Return to Olympus is an album by Malfunkshun; and (b) Malfunkshun is a rock band. Among these, only (b) is related to the necessary reasoning. Thus, achieving a high accuracy in the SF detection task does not fully prove a RC systems’s reasoning ability.\n\nThis paper proposes $\\mathcal { R } ^ { 4 } \\mathcal { C }$ , a new task of RC that requires systems to provide an answer and deriva$t i o n ^ { 1 }$ : a minimal explanation that justifies predicted answers in a semi-structured natural language form (see “Derivation” in Fig. 1 for an example). Our main contributions can be summarized as follows:\n\n• We propose $\\mathcal { R } ^ { 4 } \\mathcal { C }$ , which enables us to quantitatively evaluate a systems’ internal reasoning in a finer-grained manner than the SF detection task. We show that $\\scriptstyle \\mathcal { R } ^ { 4 } \\mathcal { C }$ assesses different skills from the SF detection task.\n\n• We create and publicly release the first dataset of $\\scriptstyle { \\mathcal { R } } ^ { 4 } { \\mathcal { C } }$ consisting of 4,588 questions, each of which is annotated with 3 high-quality derivations (i.e. 13,764 derivations), available at https://naoya-i.github. $\\mathtt { i o / r 4 c / }$ .\n\n• We present and publicly release a reliable, crowdsourced framework for scalably annotating existing RC datasets with derivations in order to facilitate large-scale dataset construction of derivations in the RC community.\n\n# 2 Task description\n\n# 2.1 Task definition\n\nWe build $\\mathcal { R } ^ { 4 } \\mathcal { C }$ on top of the standard RC task. Given a question $q$ and articles $R$ , the task is (i) to find the answer $a$ from $R$ and (ii) to generate a derivation $D$ that justifies why $a$ is believed to be the answer to q.\n\nThere are several design choices for derivations, including whether derivations should be structured, whether the vocabulary should be closed, etc. This leads to a trade-off between the expressivity of reasoning and the interpretability of an evaluation metric. To maintain a reasonable trade-off, we choose to represent derivations in a semi-structured natural language form. Specifically, a derivation is defined as a set of derivation steps. Each derivation step $d _ { i } \\in D$ is defined as a relational fact, i.e. $d _ { i } \\equiv \\langle d _ { i } ^ { h } , d _ { i } ^ { r } , d _ { i } ^ { t } \\rangle$ , where $d _ { i } ^ { h }$ , $d _ { i } ^ { t }$ are entities (noun phrases), and $d _ { i } ^ { r }$ is a verb phrase representing a relationship between $d _ { i } ^ { t }$ and $d _ { i } ^ { h }$ (see Fig. 1 for an example), similar to the Open Information Extraction paradigm (Etzioni et al., 2008). $d _ { i } ^ { h } , d _ { i } ^ { r } , d _ { i } ^ { t }$ may be a phrase not contained in $R$ (e.g. is lead singer of in Fig. 1).\n\n# 2.2 Evaluation metrics\n\nWhile the output derivations are semi-structured, the linguistic diversity of entities and relations still prevents automatic evaluation. One typical solution is crowdsourced judgement, but it is costly both in terms of time and budget. We thus resort to a reference-based similarity metric.\n\nSpecifically, for output derivation $D$ , we assume $n$ sets of golden derivations $G _ { 1 } , G _ { 2 } , . . . , G _ { n }$ . For evaluation, we would like to assess how well derivation steps in $D$ can be aligned with those in $G _ { i }$ in the best case. For each golden derivation $G _ { i }$ , we calculate $c ( D ; G _ { i } )$ , an alignment score of $D$ with respect to $G _ { i }$ or a soft version of the number of correct derivation steps in $D$ (i.e. $0 \\leq c ( D ; G _ { i } ) \\leq$ $\\operatorname* { m i n } ( | D | , | G _ { i } | ) )$ . We then find a golden derivation $G ^ { * }$ that gives the highest $c ( D ; G ^ { * } )$ and define the precision, recall and $\\mathrm { f _ { 1 } }$ as follows:\n\n$$\n\\begin{array} { l } { \\displaystyle \\mathrm { p r } ( D ) = \\frac { c ( D ; G ^ { * } ) } { | D | } , \\mathrm { r c } ( D ) = \\frac { c ( D ; G ^ { * } ) } { | G ^ { * } | } } \\\\ { \\displaystyle \\mathrm { f } _ { 1 } ( D ) = \\frac { 2 \\cdot \\mathrm { p r } ( D ; G ^ { * } ) \\cdot \\mathrm { r c } ( D ; G ^ { * } ) } { \\mathrm { p r } ( D ; G ^ { * } ) + \\mathrm { r c } ( D ; G ^ { * } ) } } \\end{array}\n$$\n\nAn official evaluation script is available at https: //naoya-i.github.io/r4c/.\n\nAlignment score To calculate $c ( D ; G _ { i } )$ , we would like to find the best alignment between derivation steps in $D$ and those in $G _ { i }$ . See Fig. 2 for an example, where two possible alignments $A _ { 1 } , A _ { 2 }$ are shown. As derivation steps in $D$ agree with those in $G _ { i }$ with $A _ { 2 }$ more than those with $A _ { 1 }$ , we would like to consider $A _ { 2 }$ when evaluating. We first define $c ( D ; G _ { i } , A _ { j } )$ , the correctness of $D$ given a specific alignment $A _ { j }$ , and then pick the\n\nOutput D Golden G [Malfunkshun] is   \n[Return to Olympus] A 0.1 [a rock band] is [Andrew Wood] [an album] 0.1 A2 is lead singer of [Malfunkshun] [Malfunkshun] 0.05 [Malfunkshun] is former of is former of   \n[Mother Love Bone] 1.0 A2 [Mother Love Bone] [Andrew Wood] [Andrew Wood] 0.2 is a member of   \ndied before the release [Mother Love Bone] of [Apple] [Andrew Wood] 0.8 died just before the A release of [Apple]\n\n# Question:\n\nThe 1988 American comedy film, The Great Outdoors,starred a four-time Academy Awardnominee,who receivedastar on the Hollywood Walkof Fame inwhat year? Articles: Reasoning: Select sentences relevant to your reasoning. Write your reasoning steps in a simple form Please use highlighted sentences as much as subject-verb-object.You may rely on words possible (non-highlighted one can be used if from \"suggestions\" generated automatically, necessary). but editing may be needed. Article 1: The Great Outdoors (film) It stars Dan Aykroyd, John Candy, Stephanie Faracy and Annette Bening in her film debut. The Great Outdoors isa 1988 Americancomedy filmdirected by Howard Deutch,and written From theabove sentence,what and produced by John Hughes. information did you infer? Itstars DanAykroyd,John Candy Who/what: StephanieFaracyandAnnette The Great Outdoors (film) Bening in her film debut. Suggestions: The Great Outdoors (film) Dan Aykroyd John Candy Stephanie Faracy Annette Bening Question: The 1988 American comedy film, her film debut The Great Outdoors, starred a four-time Academy Award nominee,who received a Did what: star on the Hollywood Walk of Fame in what star year?\n\nbest alignment as follows:\n\n$$\n\\begin{array} { c } { { c ( D ; G _ { i } , A _ { j } ) = \\displaystyle \\sum _ { ( d _ { j } , g _ { j } ) \\in A _ { j } } a ( d _ { j } , g _ { j } ) } } \\\\ { { \\mathrm { c } ( D ; G _ { i } ) = \\displaystyle \\operatorname* { m a x } _ { A _ { j } \\in A ( D , G _ { i } ) } c ( D ; G _ { i } , A _ { j } ) , } } \\end{array}\n$$\n\nwhere $a ( d _ { j } , g _ { j } )$ is a similarity $[ 0 , 1 ]$ between two derivation steps $d _ { j } , g _ { j }$ , and $\\mathcal { A } ( D , G _ { i } )$ denotes all possible one-to-one alignments between derivation steps in $D$ and those in $G _ { i }$ .\n\nFor $a ( d _ { j } , g _ { j } )$ , we consider three variants, depending on the granularity of evaluation. We first introduce two fine-grained scorer, taking only entities or relations into account (henceforth, entity scorer and relation scorer):\n\n$$\n\\begin{array} { l } { { \\displaystyle a ^ { \\mathrm { e n t } } ( d _ { j } , g _ { j } ) = \\frac { 1 } { 2 } ( \\mathrm { s } ( d _ { j } ^ { h } , g _ { j } ^ { h } ) + \\mathrm { s } ( d _ { j } ^ { t } , g _ { j } ^ { t } ) ) } } \\\\ { { \\displaystyle a ^ { \\mathrm { r e l } } ( d _ { j } , g _ { j } ) = \\mathrm { s } ( d _ { j } ^ { r } , g _ { j } ^ { r } ) , } } \\end{array}\n$$\n\nwhere $\\mathrm { s } ( \\cdot , \\cdot )$ denotes an arbitrary similarity measure $[ 0 , 1 ]$ between two phrases. In this study, we employ a normalized Levenshtein distance. Finally, as a rough indication of overall performance, we also provide a full scorer as follows:\n\n$$\na ^ { \\mathrm { f u l l } } ( d _ { j } , g _ { j } ) = \\frac { 1 } { 3 } ( \\mathrm { s } ( d _ { j } ^ { h } , g _ { j } ^ { h } ) + \\mathrm { s } ( d _ { j } ^ { r } , g _ { j } ^ { r } ) + \\mathrm { s } ( d _ { j } ^ { t } , g _ { j } ^ { t } ) )\n$$\n\n# 3 Data collection\n\nThe main purpose of $\\mathcal { R } ^ { 4 } \\mathcal { C }$ is to benchmark an RC systems’ internal reasoning. We thus assume a semi-supervised learning scenario where RC systems are trained to answer a given question on a large-scale RC dataset and then fine-tuned to give a correct reasoning on a smaller reasoning-annotated datasets. To acquire a dataset of derivations, we use crowdsourcing (CS).\n\n# 3.1 Crowdsourcing interface\n\nWe design our interface to annotate existing RC datasets with derivations, as a wide variety of high quality RC datasets are already available (Welbl et al., 2018; Yang et al., 2018, etc.). We assume that RC datasets provide (i) a question, (ii) the answer, and (iii) supporting articles, articles that support the answer (optionally with SFs).\n\nInitially, in order to encourage crowdworkers (henceforth, workers) to read the supporting articles carefully, we ask workers to answer to the question based on the supporting articles (see Appendix A). To reduce the workload, four candidate answers are provided.2 We also allow for neither as RC datasets may contain erroneous instances.\n\nSecond, we ask workers to write derivations for their answer (see Fig. 3). They click on a sentence (either a SF or non-SF) in a supporting article (left) and then input their derivation in the form of triplets (right). They are asked to input entities and relations through free-form textboxes. To reduce the workload and encourage annotation consistency, we also provide suggestions. These suggestions include predefined prepositions, noun phrases, and verb phrases automatically extracted from supporting articles.3 We also highlight SFs if they are available for the given RC dataset.\n\nTable 1: Statistics of $\\scriptstyle \\mathcal { R } ^ { 4 } \\mathcal { C }$ corpus. “st.” denotes the number of derivation steps. Each instance is annotated with 3 golden derivations.   \n\n<html><body><table><tr><td>Split</td><td>#QA</td><td colspan=\"4\"># derivations</td></tr><tr><td></td><td></td><td>2 st.</td><td>3 st.</td><td>≥ 4 st.</td><td>Total</td></tr><tr><td>train</td><td>2,379</td><td>4,944</td><td>1,553</td><td>640</td><td>7,137</td></tr><tr><td>dev</td><td>2,209</td><td>4,424</td><td>1,599</td><td>604</td><td>6,627</td></tr><tr><td>total</td><td>4,588</td><td>9,368</td><td>3,152</td><td>1,244</td><td>13,764</td></tr></table></body></html>\n\n# 3.2 Workflow\n\nTo discourage noisy annotations, we first deploy a qualification test. We provide the same task described in $\\ S 3 . 1$ in the test and manually identify competent workers in our task. The final annotation is carried out solely by these qualified workers.\n\nWe deploy the task on Amazon Mechanical Turk (AMT).4 We allow workers with $\\geq 5 { , } 0 0 0$ Human Intelligence Tasks experience and an approval rate of $\\geq 9 5 . 0 \\%$ to take the qualification test. For the test, we pay $ \\phi 1 5$ as a reward per instance. For the final annotation task, we assign 3 workers per instance and pay $\\mathfrak { c } 3 0$ to each worker.\n\n# 3.3 Dataset\n\nThere are a large number of choices of RC datasets that meet the criteria described in $\\ S 3 . 1$ including SQuAD (Rajpurkar et al., 2016) and WikiHop (Welbl et al., 2018). Our study uses HotpotQA (Yang et al., 2018), one of the most actively used multi-hop QA datasets.5 The multi-hop QA setting ensures that derivation steps are spread across documents, thereby posing an interesting unsolved research problem.\n\nFor annotation, we sampled 3,000 instances from 90,564 training instances and 3,000 instances from 7,405 development instances. For the qualification test and interface development, we sampled another 300 instances from the training set. We used the annotations of SFs provided by HotpotQA. We assume that the training set is used for fine-tuning RC systems’ internal reasoning, and the development set is used for evaluation.\n\n# 3.4 Statistics\n\nIn the qualification test, we identified 45 competent workers (out of 256 workers). To avoid noisy annotations, we filter out submissions (i) with a wrong answer and (ii) with a neither answer. After the filtering, we retain only instances with exactly three derivations annotated. Finally, we obtained 7,137 derivations for 2,379 instances in the training set and 7,623 derivations for 2,541 instances in the dev set. See Appendix B for annotation examples.\n\n# 4 Evaluation\n\n# 4.1 Methodology\n\nTo check whether annotated derivations help humans recover answers, we setup another CS task on AMT (answerability judgement). Given a HotpotQA question and the annotated derivation, 3 workers are asked whether or not they can answer the question solely based on the derivation at three levels. We evaluate all 7,623 derivations from the dev set. For reliability, we targeted only qualified workers and pay $ \\mathfrak { \\alpha } 1 5$ as a reward per instance.\n\nTo see if each derivation step can actually be derived from its source SF, we asked two expert annotators (non co-authors) to check 50 derivation steps from the dev set (derivability judgement).\n\n# 4.2 Results\n\nFor the answerability judgement, we obtained Krippendorff’s $\\alpha$ of 0.263 (a fair agreement). With majority voting, we obtained the following results: YES: $9 5 . 2 \\%$ , LIKELY: $2 . 2 \\%$ , and NO: $1 . 3 \\%$ (split: $1 . 3 \\% \\%$ ).6 For the derivability judgement, $9 6 . 0 \\%$ of the sampled derivation steps (48/50) are judged as derivable from their corresponding SFs by both expert annotators. Despite the complexity of the annotation task, the results indicate that the proposed annotation pipeline can capture competent workers and produce high-quality derivation annotations. For the final dev set, we retain only instances with YES answerability judgement.\n\nThe final $\\scriptstyle \\mathcal { R } ^ { 4 } \\mathcal { C }$ dataset includes 4,588 questions from HotpotQA (see Table 1), each of which is annotated with 3 reference derivations (i.e. 13,764 derivations). This is the first dataset of RC annotated with semi-structured, multiple reference derivations. The most closest work to our dataset is the WorldTree corpus (Jansen et al., 2018), the largest QA dataset annotated with explanations, which contains 1,680 questions. Jansen et al. (2018) use experts for annotation, and the annotated explanations are grounded on a predefined, structured knowledge base. In contrast, our work proposes a non-expert-based annotation framework and grounds explanations using unstructured texts.\n\nTable 2: Performance of oracle annotators on $\\scriptstyle \\mathcal { R } ^ { 4 } \\mathcal { C }$ as a function of the number of reference derivations.   \n\n<html><body><table><tr><td>#rf</td><td>Entity P/R/F</td><td>Relation P/R/F</td><td>Full P/R/F</td></tr><tr><td>1</td><td>73.3/75.1/73.4</td><td>56.9/55.6/55.5</td><td>70.1/69.5/69.0</td></tr><tr><td>2</td><td>79.4/77.6/77.6</td><td>66.7/65.4/65.3</td><td>74.7/73.2/73.2</td></tr><tr><td>3</td><td>83.4/81.1/81.4</td><td>72.3/69.4/70.0</td><td>77.7/75.1/75.6</td></tr></table></body></html>\n\n# 5 Analysis\n\nEffect of multiple references Do crowdsourced multiple golden derivations help us to evaluate output derivations more accurately? To verify this, we evaluated oracle derivations using one, two, or all three references. The derivations were written by qualified workers for $1 0 0 \\mathrm { d e v }$ instances.\n\nTable 2 shows that having more references increases the performance, which indicates that references provided by different workers are indeed diverse enough to capture oracle derivations. The peak performance with # $\\div \\mathrm { r f } = 3$ establishes the upper bound performance on this dataset.\n\nThe larger improvement of the relation-level performance $( + 1 4 . 5 ) \\$ compared to that of the entitylevel performance $\\left( + 8 . 0 \\right)$ also suggests that relations are linguistically more diverse than entities, as we expected (e.g. is in, is a town in, and is located in are annotated for a locational relation).\n\nBaseline models To analyze the nature of $\\mathcal { R } ^ { 4 } \\mathcal { C }$ , we evaluate the following heuristic models. IE: extracting all entity relations from SFs.7 CORE: extracting the core information of SFs. Based on the dependency structure of SFs (with article title $t \\sp { \\prime }$ , it extracts a root verb $v$ and the right, first child $c _ { r }$ of $v$ , and outputs $\\left. t , v , c _ { r } \\right.$ as a derivation step.\n\nTable 3 shows a large performance gap to the human upper bound, indicating that $\\scriptstyle \\mathcal { R } ^ { 4 } \\mathcal { C }$ is different to the HotpotQA’s SF detection task—it does not simply require systems to exhaustively extract information nor to extract core information from SFs. The errors from these baseline models include generating entity relations irrelevant to reasoning (e.g. Return to Olympus is an album in Fig. 2) or missing implicit entity relations (e.g. Andrew Wood is a member of Mother Love Bone in Fig. 1). $\\mathcal { R } ^ { 4 } \\mathcal { C }$ introduces a new research problem for developing RC systems that can explain their answers.\n\nTable 3: Performance of baseline models on $\\scriptstyle \\mathcal { R } ^ { 4 } \\mathcal { C }$ .   \n\n<html><body><table><tr><td>Model</td><td>Entity P/R/F</td><td>Relation P/R/F</td><td>Full P/R/F</td></tr><tr><td>IE</td><td>11.3/53.4/16.6</td><td>13.7/62.8/19.9</td><td>11.4/52.3/16.5</td></tr><tr><td>CORE</td><td>66.4/60.1/62.1</td><td>51.0/46.0/47.5</td><td>59.4/53.6/55.4</td></tr></table></body></html>\n\n# 6 Conclusions\n\nTowards evaluating RC systems’ internal reasoning, we have proposed $\\scriptstyle { \\mathcal { R } } ^ { 4 } { \\mathcal { C } }$ that requires systems not only to output answers but also to give their derivations. For scalability, we have carefully developed a crowdsourced framework for annotating existing RC datasets with derivations. Our experiments have demonstrated that our framework produces high-quality derivations, and that automatic evaluation metrics using multiple reference derivations can reliably capture oracle derivations. The experiments using two simple baseline models highlight the nature of $\\scriptstyle \\mathcal { R } ^ { 4 } \\mathcal { C }$ , namely that the derivation generation task is not simply the SF detection task. We make the dataset, automatic evaluation script, and baseline systems publicly available at https://naoya-i.github. $\\mathtt { i o / r 4 c / }$ .\n\nOne immediate future work is to evaluate stateof-the-art RC systems’ internal reasoning on our dataset. For modeling, we plan to explore recent advances in conditional language models for jointly modeling QA with generating their derivations.\n\n# Acknowledgements\n\nThis work was supported by the UCL-Tohoku University Strategic Partnership Fund, JSPS KAKENHI Grant Number 19K20332, JST CREST Grant Number JPMJCR1513 (including the AIP challenge program), the European Union’s Horizon 2020 research and innovation programme under grant agreement No 875160, and the UK Defence Science and Technology Laboratory (Dstl) and Engineering and Physical Research Council (EPSRC) under grant EP/R018693/1 (a part of the collaboration between US DOD, UK MOD, and UK EPSRC under the Multidisciplinary University Research Initiative (MURI)). The authors would like to thank Paul Reisert, Keshav Singh, other members of the Tohoku NLP Lab, and the anonymous reviewers for their insightful feedback.\n\n# References\n\nGabor Angeli, Melvin Johnson Premkumar, and Christopher D. Manning. 2015. Leveraging linguistic structure for open domain information extraction. In Proc. of ACL-IJCNLP, pages 344–354.   \nOana-maria Camburu, Tim Rockta¨schel, Thomas Lukasiewicz, and Phil Blunsom. 2018. e-SNLI : Natural Language Inference with Natural Language Explanations. In Proc. of NIPS, pages 1–13.   \nJifan Chen and Greg Durrett. 2019. Understanding Dataset Design Choices for Multi-hop Reasoning. In Proc. of NAACL-HLT, pages 4026–4032.   \nOren Etzioni, Michele Banko, Stephen Soderland, and Daniel S. Weld. 2008. Open information extraction from the web. Communications of the ACM, 51(12):68–74.   \nAngela Fan, Yacine Jernite, Ethan Perez, David Grangier, Jason Weston, and Michael Auli. 2019. ELI5: Long Form Question Answering. In Proc. of ACL, pages 3558–3567.   \nPeter A. Jansen. 2018. Multi-hop Inference for Sentence-level TextGraphs: How Challenging is Meaningfully Combining Information for Science Question Answering? In Proc. of TextGraphs-12, pages 12–17.   \nPeter A. Jansen, Elizabeth Wainwright, Steven Marmorstein, and Clayton T. Morrison. 2018. WorldTree: A Corpus of Explanation Graphs for Elementary Science Questions supporting Multi-Hop Inference. In Proc. of LREC, pages 2732–2740.   \nYichen Jiang, Nitish Joshi, Yen-Chun Chen, and Mohit Bansal. 2019. Explore, Propose, and Assemble: An Interpretable Model for Multi-Hop Reading Comprehension. In Proc. of ACL, pages 2714–2725.   \nToma´sˇ Kocˇisk\\`y, Jonathan Schwarz, Phil Blunsom, Chris Dyer, Karl Moritz Hermann, Ga´bor Melis, and Edward Grefenstette. 2018. The NarrativeQA Reading Comprehension Challenge. Trans. of ACL, 6:317–328.   \nSewon Min, Eric Wallace, Sameer Singh, Matt Gardner, Hannaneh Hajishirzi, and Luke Zettlemoyer. 2019. Compositional Questions Do Not Necessitate Multi-hop Reasoning. In Proc. of ACL, pages 4249– 4257.   \nPramod K. Mudrakarta, Ankur Taly, Mukund Sundararajan, and Kedar Dhamdhere. 2018. Did the Model Understand the Question? In Proc. of ACL, pages 1896–1906.   \nFatema Nanzneen Rajani, Bryan McCann, Caiming Xiong, and Richard Socher. 2019. Explain Yourself ! Leveraging Language Models for Commonsense Reasoning. In Proc. of ACL, pages 4932–4942.   \nPranav Rajpurkar, Jian Zhang, Konstantin Lopyrev, and Percy Liang. 2016. SQuAD: $1 0 0 { , } 0 0 0 { + }$ Questions for Machine Comprehension of Text. In Proc. of EMNLP, pages 2383–2392.   \nSaku Sugawara, Kentaro Inui, Satoshi Sekine, and Akiko Aizawa. 2018. What Makes Reading Comprehension Questions Easier? In Proc. of EMNLP, pages 4208–4219.   \nJames Thorne and Andreas Vlachos. 2018. Automated Fact Checking: Task formulations, methods and future directions. In Proc. of COLING, pages 3346– 3359.   \nJohannes Welbl, Pontus Stenetorp, and Sebastian Riedel. 2018. Constructing Datasets for Multi-hop Reading Comprehension Across Documents. Trans. of ACL, 6:287–302.   \nZhilin Yang, Peng Qi, Saizheng Zhang, Yoshua Bengio, William W. Cohen, Ruslan Salakhutdinov, and Christopher D. Manning. 2018. HotpotQA: A Dataset for Diverse, Explainable Multi-hop Question Answering. In Proc. of EMNLP, pages 2369– 2380.\n\n# A Crowdsourcing interface\n\nFig. 4 shows the instruction of our annotation task to crowdworkers. Fig. 5 shows the interface of the question-answering task.\n\n# Overview\n\nArtifialIntellience (A)hasbecomefsandacurate.Howeveittilfalssrtitcomesthumanvelreasog.nths task,youarehelpinguseducateanAlhowtoreason.Specificalywewilaskyoutoverifywhetheryucanansweraquestionfrom articles and describe the steps you used to find the answer.\n\n# Instructions\n\n# View instructions (for first time workers)\n\n# Overview\n\n1. Read a given question and related articles.   \n2.Answer to the question solely based on the information from each article.   \n3.DescribeyoureasonngonowtoreachtheanswerEachreasonngstepneedstobinasimplesubjectverbbjectfo(seeexampleelow). Your reasoning must include sentences containing your answer.\n\nYou can consult each section for further instructions.\n\n# Example Question\n\nWhen is Barack Obama's wife's birthday?\n\n# Articles\n\n$\\cdot \\cdot$ Article1: Barack Obama isa former US president.Hiswife is Michelle Obama.   \n·Article 2: Michelle Obama is a former US first lady (born January 17, 1964).\n\n# Answer\n\nJanuary 17, 1964\n\n# Reasoning\n\nSee both a good response and bad response below:\n\nReasoning Good/Bad Article 1,second sentence $\\Rightarrow$ [Barack GooDNote that the second reasoning step includes [January 17,1964], the answer. Obama]'s wife is [Michelle Obama]. ·Article 2, first sentence $\\Rightarrow$ [Michelle Obama] is born on [January ${ 1 7 } _ { \\cdot }$ 1964]. Article 2,first sentence $\\Rightarrow$ [Michelle BADThe reasoning does not state the relation between [Michelle Obama] and [Barack Obama]. It is Obama] is born on [January ${ 1 7 } _ { \\cdot }$ 1964]. obvious for us,but it is not for Als.Do not forget write down such things in this task! Article 1,second sentence $\\Rightarrow$ [Barack BADThe second reasoning step includes [on January 17,1964],which is not an object. Obama]'s wife is [Michelle Obama]. ·Article 2, first sentence $\\Rightarrow$ [Michelle Obama] is born [on January 17, 1964].\n\n# Task\n\n# 1. Read\n\nRead the following question and related articles carefuly.Important sentences are highlighted.\n\n# Question\n\nThe1988AmericacomedyfmeGreatOutdoors,sarrdfour-tiecadeyAwadomie,receivedstaroteHlyodWalkof Fame in what year?\n\n# Articles\n\nArticle 1: The Great Outdoors (film)\n\nTheGreatOutdoors isa1988Americancomedyfimdirectedby HowardDeutch,and writenand producedbyJohn Hughes.   \nIt stars Dan Aykroyd,John Candy,Stephanie Faracyand Annette Bening inher filmdebut.\n\n# Article 2: Annette Bening\n\n# AnnetteCarol Bening (bornMay29,1958) isanAmericanactress.\n\nShebeganhercareronstagewiththeColoradoShakespeareFestivalcompanyin1980andplayedLadyMacbeth in1984atthemerican Conservatory Theatre\n\nShe was nominatedforthe1987TonyAwardfor BestFeatured Actress inaPlayfor her Broadwaydebutin \"Coastal Disturbances\"\n\nSheisafourtidadffts\"99)，maBety(9)eingJia4)dTdt (2010).\n\nIn 2006, she received a star on the Hollywood Walk of Fame.\n\n# 2.Answer\n\nBased upon the related articles,answer to the question.\n\n# NOTE:\n\n· If you found multiple possible answers,please choose one of them and write your reasoning for it below.   \n·Ifthequestioass):iyuapreedatoetestiiut(.td not make sense),select \"NONE OFTHE ABOVE\".\n\nThe1988AmericaomedfieGeatOudostarrdurtiadeAadomeeivdroteyodWalkf Fame in what year?\n\n# Choose your answer:\n\n<html><body><table><tr><td>√FarrahFawcett</td><td></td></tr><tr><td>2006</td><td></td></tr><tr><td></td><td>Shane Stanley</td></tr><tr><td></td><td>Beau Bridges</td></tr><tr><td></td><td>NONE OF THE ABOVE</td></tr></table></body></html>\n\nFigure 5: Task interface for the first question answering phase. The reasoning annotation interface shown in Fig. 3 follows after this interface.\n\n# B Example annotations\n\nTable 4 shows examples of crowdsourced annotations.\n\n<html><body><table><tr><td>Question Supporting Art. 1</td><td>Were Scott Derrickson and Ed Wood of the same nationality? [1] Scott Derrickson (born July16,1966) is an American director,screenwriter and producer.[2] He</td></tr><tr><td></td><td>lives inLos Angeles,California.[3] He is best known for directing horror films such as \"Sinister”, \"The Exorcism of Emily Rose\",and \"Deliver Us From Evil\",as well as the 2016 Marvel Cinematic Universe installment,\"Doctor Strange.\" [1] Edward Davis Wood Jr.(October 10,1924 December10,1978)was an American filmmaker,</td></tr><tr><td>Supporting Art. 2 Derivation step 1</td><td>actor,writer,producer,and director. [1,1] [Scot Derrickson] [is] [an American director]</td></tr><tr><td>Derivation step 2</td><td>[1,1][Ed Wood] [was] [an American filmmaker]</td></tr><tr><td>Question Supporting Art. 1</td><td>The director of the romantic comedy \"Big Stone Gap”is based in what New York city? [1] Big Stone Gap is a 2Ol4 American drama romantic comedy film writen and directed by Adriana Trigiani and produced by Donna Gigliotti forAltar Identity Studios,a subsidiary ofMedia Society.[2]</td></tr><tr><td></td><td>Based on Trigiani's 2Ooo best-selling novel of the same name,the story is set in the actual Virginia town of Big Stone Gap circa 197Os.[3] The film had its world premiere at the Virginia Film Festival</td></tr><tr><td>Supporting Art. 2</td><td>on November 6,2014. [1] Adriana Trigiani is an Italian American best-selling author of sixteen books,television writer,</td></tr><tr><td></td><td>film director,and entrepreneur based in Greenwich Village,New York City.[2] Trigiani has published a novel a year since 2000. [1,1][Big Stone Gap] [is directed by][Adriana Trigiani]</td></tr><tr><td>Derivation step 1 Derivation step 2</td><td>[2,1][Adriana Trigiani] [is from] [Greenwich Village,New York City.]</td></tr><tr><td>Question Supporting Art. 1</td><td>The arena where the Lewiston Maineiacs played their home games can seat how many people? [1] The Lewiston Maineiacs were a junior ice hockey team of the Quebec Major Junior Hockey</td></tr><tr><td></td><td>League based in Lewiston,Maine.[2] The team played its home games at the Androscoggin Bank Colise.[3] They were the second QMJHL team in the United States,and the only one to play a full season.[4] They won the President's Cup in 2007.</td></tr><tr><td>Supporting Art. 2</td><td>[1] The Androscoggin Bank Colise (formerly Central Maine Civic Center and Lewiston Colisee) is a</td></tr><tr><td></td><td>4,000 capacity (3,677 seated) multi-purpose arena,in Lewiston,Maine,that opened in 1958.[2] In 1965 it was the location of the World Heavyweight Title fight during which one of the most famous</td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td>sports photographs of the century was taken of Muhammed Ali standing over Sonny Liston.</td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td>Derivation step 1</td><td>[1,2] [Lewiston Maineiacs] [play in the] [Androscoggin Bank Colise]</td></tr><tr><td></td><td></td></tr><tr><td>Derivation step 2</td><td>[2,1] [Androscoggin Bank Colise] [is an] [arena]</td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td>Derivation step 3</td><td>[2,1] [Androscoggin Bank Colise] [has a seating capacity of] [3,677 seated]</td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr><tr><td></td><td></td></tr></table></body></html>\n\nTable 4: Example of annotation results of derivations. Each derivation step is in the following format: [article ID, SF] [Head entity] [Relation] [Tail entity]."}