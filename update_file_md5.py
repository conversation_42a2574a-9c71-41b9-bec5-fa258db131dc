#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本用于恢复JSON文件的FILE_MD5字段并重命名文件
1. 从CSV文件中读取原始MD5值
2. 恢复JSON文件中的FILE_MD5字段
3. 将JSON文件重命名为MD5值
"""

import json
import os
import csv
import glob
import shutil
from pathlib import Path

def read_csv_md5_mapping(csv_file):
    """
    从CSV文件中读取文件名到MD5的映射

    Args:
        csv_file (str): CSV文件路径

    Returns:
        dict: 文件名到MD5的映射字典
    """
    mapping = {}
    try:
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            print(f"CSV文件列名: {reader.fieldnames}")

            for i, row in enumerate(reader):
                print(f"处理第 {i+1} 行: 序号={row.get('序号', 'N/A')}, 文件名称={row.get('文件名称', 'N/A')}")

                if '文件名称' in row and row['文件名称']:
                    filename = row['文件名称'].strip()
                    if filename.endswith('.json'):
                        # 提取MD5值（去掉.json扩展名）
                        md5_value = filename[:-5]  # 去掉.json
                        # 当前文件名格式（6位数字）
                        if '序号' in row and row['序号']:
                            current_filename = f"{int(row['序号']):06d}.json"
                            mapping[current_filename] = md5_value
                            print(f"  添加映射: {current_filename} -> {md5_value}")

                # 只处理前5行用于调试
                if i >= 4:
                    print("  ... (仅显示前5行调试信息)")
                    break

        print(f"从CSV文件中读取到 {len(mapping)} 个映射关系")
        return mapping
    except Exception as e:
        print(f"读取CSV文件失败: {e}")
        return {}

def restore_and_rename_files(mapping):
    """
    恢复FILE_MD5字段并重命名文件

    Args:
        mapping (dict): 文件名到MD5的映射

    Returns:
        tuple: (成功数量, 总数量)
    """
    success_count = 0
    total_count = len(mapping)

    for current_filename, md5_value in mapping.items():
        try:
            if not os.path.exists(current_filename):
                print(f"警告: 文件 {current_filename} 不存在")
                continue

            # 读取JSON文件
            with open(current_filename, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 恢复FILE_MD5字段
            old_value = data.get('FILE_MD5', 'unknown')
            data['FILE_MD5'] = md5_value

            # 写回JSON文件
            with open(current_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)

            # 重命名文件
            new_filename = f"{md5_value}.json"
            if current_filename != new_filename:
                if os.path.exists(new_filename):
                    print(f"警告: 目标文件 {new_filename} 已存在，跳过重命名")
                else:
                    os.rename(current_filename, new_filename)
                    print(f"✓ {current_filename} -> {new_filename} (FILE_MD5: {old_value} -> {md5_value})")
            else:
                print(f"✓ {current_filename} FILE_MD5已恢复: {old_value} -> {md5_value}")

            success_count += 1

        except json.JSONDecodeError as e:
            print(f"错误: {current_filename} 不是有效的JSON文件: {e}")
        except Exception as e:
            print(f"错误: 处理 {current_filename} 时发生异常: {e}")

    return success_count, total_count

def main():
    """
    主函数
    """
    csv_file = "论文数据集_rag_processed.csv"

    if not os.path.exists(csv_file):
        print(f"错误: CSV文件 {csv_file} 不存在")
        return

    print("开始从CSV文件读取MD5映射...")
    mapping = read_csv_md5_mapping(csv_file)

    if not mapping:
        print("没有找到有效的映射关系")
        return

    print("开始恢复FILE_MD5字段并重命名文件...")
    print("-" * 60)

    success_count, total_count = restore_and_rename_files(mapping)

    print("-" * 60)
    print(f"处理完成: {success_count}/{total_count} 个文件成功处理")

    if success_count < total_count:
        print(f"有 {total_count - success_count} 个文件处理失败，请检查上述错误信息")

if __name__ == "__main__":
    main()
