#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本用于批量更新JSON文件中的FILE_MD5字段
将FILE_MD5的值设置为JSON文件的文件名（不包含.json扩展名）
"""

import json
import os
import glob
from pathlib import Path

def update_json_file_md5(file_path):
    """
    更新单个JSON文件的FILE_MD5字段
    
    Args:
        file_path (str): JSON文件路径
    
    Returns:
        bool: 更新是否成功
    """
    try:
        # 获取文件名（不包含扩展名）
        filename = Path(file_path).stem
        
        # 读取JSON文件
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检查是否存在FILE_MD5字段
        if 'FILE_MD5' not in data:
            print(f"警告: {file_path} 中没有找到 FILE_MD5 字段")
            return False
        
        # 更新FILE_MD5字段
        old_value = data['FILE_MD5']
        data['FILE_MD5'] = filename
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        
        print(f"✓ 已更新 {file_path}: {old_value} -> {filename}")
        return True
        
    except json.JSONDecodeError as e:
        print(f"错误: {file_path} 不是有效的JSON文件: {e}")
        return False
    except Exception as e:
        print(f"错误: 处理 {file_path} 时发生异常: {e}")
        return False

def main():
    """
    主函数：批量处理当前目录下的所有JSON文件
    """
    # 获取当前目录下所有的JSON文件
    json_files = glob.glob("*.json")
    
    if not json_files:
        print("当前目录下没有找到JSON文件")
        return
    
    print(f"找到 {len(json_files)} 个JSON文件")
    print("开始处理...")
    print("-" * 50)
    
    success_count = 0
    total_count = len(json_files)
    
    # 按文件名排序
    json_files.sort()
    
    for json_file in json_files:
        if update_json_file_md5(json_file):
            success_count += 1
    
    print("-" * 50)
    print(f"处理完成: {success_count}/{total_count} 个文件成功更新")
    
    if success_count < total_count:
        print(f"有 {total_count - success_count} 个文件处理失败，请检查上述错误信息")

if __name__ == "__main__":
    main()
